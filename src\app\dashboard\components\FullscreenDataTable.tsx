import { Table, TableBody, TableCell, TableHead, TableRow, Box } from '@mui/material'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import { getTaskStateColors } from '../../../common/utils/taskStateColors'
import { useAppStore } from '../../../core/store'
import TableLegends from '../../../common/components/TableLegends'

function rowsChecksum(rows: Task[]): string {
	return JSON.stringify(
		rows.map((r) => ({
			id: r.id,
			rowName: r.rowName,
			date: r.date,
			shippingDate: r.shippingDate,
			vangp: r.vangp,
			deliveryTime: r.deliveryTime,
			plannedStart: r.plannedStart,
			plannedEnd: r.plannedEnd,
			plannedDuration: r.plannedDuration,
			actualStart: r.actualStart,
			actualEnd: r.actualEnd,
			actualDuration: r.actualDuration,
		})),
	)
}

interface FullscreenDataTableProps {
	rows: Task[]
	selectedRowId?: number | null
	onSelect?: (row: Task) => void
}

function FullscreenDataTableComponent({ rows, selectedRowId, onSelect }: FullscreenDataTableProps) {
	const { t } = useTranslation()
	const taskStateColors = useAppStore((s) => s.taskStateColors)

	// Constants for sticky header positioning
	const mainHeaderHeight = 48
	const subHeaderHeight = 48
	const mainHeaderTop = 0
	const subHeaderTop = mainHeaderHeight
	const mainHeaderZIndex = 12
	const subHeaderZIndex = 11

	return (
		<Box>
			{/* Table Legends */}
			<Box sx={{ px: 2, py: 1, flexShrink: 0 }}>
				<TableLegends compact={true} />
			</Box>
			<Box sx={{ flex: 1, overflow: 'auto' }}>
				<Table stickyHeader size="small" sx={{ border: '1px solid #E0E0E0' }}>
					<TableHead>
						{/* Top-level headers */}
						<TableRow sx={{ height: mainHeaderHeight }}>
							{[
								{ label: rows[0]?.name, colSpan: 5, backgroundColor: 'background.paper' },
								{ label: t('table.planned'), colSpan: 4, backgroundColor: '#DAE9F8' },
								{ label: t('table.actual'), colSpan: 4, backgroundColor: '#C1F0C8' },
							].map((header, idx) => (
								<TableCell
									colSpan={header.colSpan}
									key={idx}
									align="center"
									sx={{
										position: 'sticky',
										top: mainHeaderTop,
										zIndex: mainHeaderZIndex,
										backgroundColor: header.backgroundColor,
										borderBottom: '1px solid',
										borderColor: 'divider',
										fontWeight: 600,
										fontSize: '1rem',
									}}
								>
									{header.label}
								</TableCell>
							))}
						</TableRow>

						{/* Sub-level headers */}
						<TableRow sx={{ height: subHeaderHeight }}>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 500,
									minWidth: 80,
								}}
							>
								{t('table.no')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 120,
								}}
							>
								{t('table.date')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 120,
								}}
							>
								{t('table.shippingDate')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 80,
								}}
							>
								{t('table.vangp')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 120,
								}}
							>
								{t('table.deliveryTime')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.start')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.end')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.duration')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.plannedVs')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.start')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.end')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.duration')}
							</TableCell>
							<TableCell
								align="center"
								sx={{
									position: 'sticky',
									top: subHeaderTop,
									zIndex: subHeaderZIndex,
									backgroundColor: 'background.paper',
									fontWeight: 600,
									minWidth: 100,
								}}
							>
								{t('table.actualVs')}
							</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{rows.map((row, index) => {
							const colors = getTaskStateColors(row, taskStateColors)
							const isSelected = row.id === selectedRowId
							return (
								<TableRow
									key={`${row.id}-${index}`}
									hover
									selected={isSelected}
									onClick={() => onSelect?.(row)}
									sx={{
										cursor: onSelect ? 'pointer' : 'default',
										backgroundColor: `${colors.row} !important`,
										'& .MuiTableCell-root': {
											backgroundColor: 'transparent',
										},
										'&:hover': {
											backgroundColor: `${colors.row} !important`,
											filter: 'brightness(0.95)',
										},
										'&.Mui-selected': {
											backgroundColor: `${colors.header} !important`,
											'& .MuiTableCell-root': {
												color: '#fff',
											},
										},
									}}
								>
									{[
										row.rowName,
										row.date,
										row.shippingDate,
										row.vangp,
										row.deliveryTime,
										row.plannedStart,
										row.plannedEnd,
										row.plannedDuration,
										row.plannedVs,
										row.actualStart,
										row.actualEnd,
										row.actualDuration,
										row.actualVs,
									].map((data, idx) => (
										<TableCell key={idx} align="center">{data}</TableCell>
									))}
								</TableRow>
							)
						})}
					</TableBody>
				</Table>
			</Box>
		</Box>
	)
}

const FullscreenDataTable = memo(FullscreenDataTableComponent, (prev, next) => {
	return (
		rowsChecksum(prev.rows) === rowsChecksum(next.rows) &&
		prev.selectedRowId === next.selectedRowId
	)
})

export default FullscreenDataTable
