// Environment configuration
interface Config {
	apiBaseUrl: string
	debugHttp: boolean
	dataRefreshRate: number
}

interface EnvironmentVariables {
	MODE: string
	VITE_API_BASE_URL: string
	VITE_DEBUG_HTTP: string | boolean
	VITE_DATA_REFRESH_RATE: string
	VITE_USE_MSW: string | boolean
	DEV?: boolean
	PROD?: boolean
}

// Environment provider that abstracts access to environment variables
// This solves the import.meta TypeScript compilation issue in Jest
const getEnvironmentVariables = (): EnvironmentVariables => {
	// Check for Jest/test environment mock first (highest priority)
	const globalWithImport = globalThis as any
	if (globalWithImport.import?.meta?.env) {
		return globalWithImport.import.meta.env as EnvironmentVariables
	}

	// Check if we're in a browser environment with Vite
	if (typeof window !== 'undefined') {
		// In browser/Vite environment, import.meta.env should be available
		// We use a type assertion here since we know Vite provides this
		const viteEnv =
			(globalThis as any).__VITE_ENV__ ||
			(typeof (globalThis as any).import !== 'undefined' &&
				(globalThis as any).import.meta?.env)

		if (viteEnv) {
			return viteEnv as EnvironmentVariables
		}
	}

	// Fallback for Node.js/server environments
	// Use globalThis to access process safely without TypeScript errors
	const globalProcess = (globalThis as any).process
	const nodeEnv = globalProcess?.env || {}
	return {
		MODE: nodeEnv.NODE_ENV || 'development',
		VITE_API_BASE_URL: nodeEnv.VITE_API_BASE_URL || 'http://localhost:8080',
		VITE_DEBUG_HTTP: nodeEnv.VITE_DEBUG_HTTP === 'true',
		VITE_DATA_REFRESH_RATE: nodeEnv.VITE_DATA_REFRESH_RATE || '5000',
		VITE_USE_MSW: nodeEnv.VITE_USE_MSW === 'true',
		DEV: nodeEnv.NODE_ENV !== 'production',
		PROD: nodeEnv.NODE_ENV === 'production',
	}
}

// Backward compatibility alias
const getEnv = getEnvironmentVariables

// Runtime configuration loaded from public/config.json in production
let runtimeConfig: Config | null = null

// Load configuration from public/config.json
async function loadRuntimeConfig(): Promise<Config | null> {
	if (runtimeConfig) {
		return runtimeConfig
	}

	try {
		const env = getEnv()
		// console.log('loadRuntimeConfig: env.MODE =', env.MODE)

		// Only load config.json in production mode
		if (env.MODE === 'production') {
			const response = await fetch('/config.json')
			if (response.ok) {
				runtimeConfig = await response.json()
				// console.log('✓ Runtime configuration loaded from config.json')
				return runtimeConfig
			}
		}
	} catch (error) {
		console.warn('Failed to load runtime configuration:', error)
	}

	return null
}

// Get API base URL - uses runtime config in production, environment variables in development
export async function getApiBaseUrl(): Promise<string> {
	const env = getEnv()

	// In production mode, ONLY use config.json (no .env files)
	if (env.MODE === 'production') {
		const config = await loadRuntimeConfig()
		if (config?.apiBaseUrl) {
			return config.apiBaseUrl
		}
		// In production, if config.json fails, return empty string (should not happen)
		console.warn('Production mode: config.json not available, API calls may fail')
		return ''
	}

	// In development modes (dev, mock, test), use environment variables
	if (env.MODE === 'dev' || env.MODE === 'mock' || env.MODE === 'test') {
		return String(env.VITE_API_BASE_URL || '')
	}

	// Fallback (should not reach here in normal cases)
	return String(env.VITE_API_BASE_URL || '')
}

// Get debug HTTP setting
export async function getDebugHttp(): Promise<boolean> {
	const env = getEnv()

	// In production mode, ONLY use config.json (no .env files)
	if (env.MODE === 'production') {
		const config = await loadRuntimeConfig()
		if (config && typeof config.debugHttp === 'boolean') {
			return config.debugHttp
		}
		// In production, default to false if config.json fails
		return false
	}

	// In development modes (dev, mock, test), use environment variables
	if (env.MODE === 'dev' || env.MODE === 'mock' || env.MODE === 'test') {
		return String(env.VITE_DEBUG_HTTP) === 'true'
	}

	// Fallback (should not reach here in normal cases)
	return String(env.VITE_DEBUG_HTTP) === 'true'
}

// Get data refresh rate setting
export async function getDataRefreshRate(): Promise<number> {
	const env = getEnv()

	// In development, use environment variables
	if (env.MODE === 'dev' || env.MODE === 'mock' || env.MODE === 'test') {
		console.log('refresh rate from env: ', env.VITE_DATA_REFRESH_RATE)

		const refreshRate = parseInt(String(env.VITE_DATA_REFRESH_RATE || '10000'), 10)
		return isNaN(refreshRate) ? 10000 : refreshRate
	}

	// In production, try to load from config.json first
	const config = await loadRuntimeConfig()
	if (config && typeof config.dataRefreshRate === 'number') {
		return config.dataRefreshRate
	}

	// Fallback to environment variable if config.json fails
	const fallbackRate = parseInt(String(env.VITE_DATA_REFRESH_RATE || '7000'), 10)
	return isNaN(fallbackRate) ? 7000 : fallbackRate
}

// Legacy ENV object for backward compatibility (deprecated - use async functions above)
export const ENV = {
	// These will be empty in production - use getApiBaseUrl() and getDebugHttp() instead
	API_BASE_URL: String(getEnv().VITE_API_BASE_URL || ''),
	DEBUG_HTTP: String(getEnv().VITE_DEBUG_HTTP) === 'true' || false,
	DATA_REFRESH_RATE: parseInt(String(getEnv().VITE_DATA_REFRESH_RATE || '10000'), 10) || 10000,
}
