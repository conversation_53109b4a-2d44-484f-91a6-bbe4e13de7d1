import { Suspense, lazy, useEffect, useRef, useState } from 'react'
import { Paper, type PaperProps } from '@mui/material'
import FullscreenModal from '../../../common/components/FullscreenModal'
import DataTable from './DataTable'
import FullscreenView from './FullscreenView'
import type { Operator } from '../../../models/Operator'
import type { Task } from '../../../models/Task'
import { useInView } from 'react-intersection-observer'
import { fetchTasksForOperator } from '../services/tasks'
import { getDataRefreshRate } from '../../../core/config/env'

const LazyChartCard = lazy(() => import('./ChartCard'))

interface ElementCardProps extends PaperProps {
	operator: Operator
}

function isAbortError(err: unknown): boolean {
	if (err instanceof DOMException) return err.name === 'AbortError'
	if (typeof err === 'object' && err !== null && 'name' in err) {
		const name = (err as { name?: unknown }).name
		return typeof name === 'string' && name === 'AbortError'
	}
	return false
}

const ElementCard = ({ operator, ...paperProps }: ElementCardProps) => {
	const { ref, inView } = useInView({ threshold: 0.9 })
	const [tasks, setTasks] = useState<Task[]>([])
	const initial = null
	const [selected, setSelected] = useState<Task | null>(initial)
	const prevChecksumRef = useRef<string>('')

	useEffect(() => {
		const controller = new AbortController()
		fetchTasksForOperator(operator.name, controller.signal)
			.then((data) => {
				const checksum = computeChecksum(data)
				if (checksum !== prevChecksumRef.current) {
					prevChecksumRef.current = checksum
					setTasks(data)
				}
			})
			.catch((e) => {
				if (!isAbortError(e)) {
					// swallow non-abort errors for background refresh but surface could be added if needed
				}
			})
		return () => controller.abort()
	}, [operator.name])

	useEffect(() => {
		let isCancelled = false
		let intervalId: ReturnType<typeof setInterval> | undefined = undefined
		let currentController: AbortController | null = null

		const fetchAndUpdateTasks = async () => {
			if (!inView || isCancelled) return

			// Abort previous request
			if (currentController) {
				currentController.abort()
			}

			currentController = new AbortController()

			try {
				const data = await fetchTasksForOperator(operator.name, currentController.signal)
				if (isCancelled || currentController.signal.aborted) return

				console.debug('Fetched tasks:', data);
				const checksum = computeChecksum(data)
				if (checksum !== prevChecksumRef.current) {
					prevChecksumRef.current = checksum

					setTasks(data)
				}
			} catch (error) {
				if (!isAbortError(error) && !isCancelled) {
					console.error('Failed to fetch tasks:', error)
				}
			}
		}

		const initializeRefresh = async () => {
			try {
				const refreshRate = await getDataRefreshRate()
				if (isCancelled) return

				// Initial fetch
				await fetchAndUpdateTasks()

				if (isCancelled) return

				// Set up interval
				intervalId = setInterval(fetchAndUpdateTasks, refreshRate)
			} catch (error) {
				console.error('Failed to initialize refresh:', error)
			}
		}

		initializeRefresh()
		return () => {
			isCancelled = true
			clearInterval(intervalId)
		}
	}, [inView, operator.name])

	useEffect(() => {
		if (tasks.length > 0) {
			if (selected) {
				const latest = tasks.find((t) => t.id === selected.id)
				if (latest && latest !== selected) {
					setSelected(latest)
				} else if (!latest) {
					// Selected task is no longer in the array, clear selection
					setSelected(null)
				}
			}
		} else {
			// No tasks available, clear selection
			setSelected(null)
		}
	}, [tasks, selected])

	const [fsOpen, setFsOpen] = useState(false)
	const [fsTitle, setFsTitle] = useState<string | undefined>(undefined)
	const [fsViewType, setFsViewType] = useState<'table' | 'chart'>('table')

	const openFullscreen = (viewType: 'table' | 'chart', title?: string) => {
		setFsViewType(viewType)
		setFsTitle(title)
		setFsOpen(true)
	}

	const computeChecksum = (list: Task[]) =>
		JSON.stringify(
			list.map((t) => ({
				id: t.id,
				name: t.name,
				progressRate: t.progressRate,
				plannedStart: t.plannedStart,
				plannedEnd: t.plannedEnd,
				plannedDuration: t.plannedDuration,
				actualStart: t.actualStart,
				actualEnd: t.actualEnd,
				actualDuration: t.actualDuration,
			})),
		)

	return (
		<Paper
			ref={ref}
			variant="outlined"
			className={`space-y-4 w-full h-full`}
			{...paperProps}
		>
			<div className='h-full grid grid-cols-2 gap-[0.25rem]'>
				<div className="min-h-64 md:min-h-72">
					<DataTable
						rows={tasks}
						selectedRowId={selected?.id ?? null}
						onSelect={(row) => setSelected(row)}
						onFullView={openFullscreen}
					/>
				</div>
				<div className="min-h-64 md:min-h-72">
					<Suspense fallback={<div className="p-4">Loading chart…</div>}>
						<LazyChartCard selectedTask={selected} onFullView={openFullscreen} />
					</Suspense>
				</div>
			</div>
			<FullscreenModal open={fsOpen} title={fsTitle} onClose={() => setFsOpen(false)}>
				{fsOpen && (
					<FullscreenView
						operator={operator}
						initialTasks={tasks}
						initialSelectedTask={selected}
						viewType={fsViewType}
						onTaskSelect={setSelected}
					/>
				)}
			</FullscreenModal>
		</Paper>
	)
}

export default ElementCard
